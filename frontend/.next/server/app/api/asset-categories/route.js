"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/asset-categories/route";
exports.ids = ["app/api/asset-categories/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fasset-categories%2Froute&page=%2Fapi%2Fasset-categories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fasset-categories%2Froute.ts&appDir=%2Fopt%2FRS_asset%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fopt%2FRS_asset%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fasset-categories%2Froute&page=%2Fapi%2Fasset-categories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fasset-categories%2Froute.ts&appDir=%2Fopt%2FRS_asset%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fopt%2FRS_asset%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _opt_RS_asset_frontend_src_app_api_asset_categories_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/asset-categories/route.ts */ \"(rsc)/./src/app/api/asset-categories/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/asset-categories/route\",\n        pathname: \"/api/asset-categories\",\n        filename: \"route\",\n        bundlePath: \"app/api/asset-categories/route\"\n    },\n    resolvedPagePath: \"/opt/RS_asset/frontend/src/app/api/asset-categories/route.ts\",\n    nextConfigOutput,\n    userland: _opt_RS_asset_frontend_src_app_api_asset_categories_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/asset-categories/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fasset-categories%2Froute&page=%2Fapi%2Fasset-categories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fasset-categories%2Froute.ts&appDir=%2Fopt%2FRS_asset%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fopt%2FRS_asset%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/asset-categories/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/asset-categories/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// 获取资产分类列表\nasync function GET() {\n    try {\n        // 从 categories.json 文件读取数据\n        const categoriesPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"..\", \"categories.json\");\n        const categoriesData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(categoriesPath, \"utf8\");\n        const categories = JSON.parse(categoriesData);\n        // 为每个分类添加 attributes 字段以保持兼容性\n        const categoriesWithAttributes = categories.map((category)=>({\n                ...category,\n                attributes: category.attributes || {\n                    codeRule: {\n                        digits: 4,\n                        prefix: category.code?.split(\"-\").pop() || \"ASSET\"\n                    },\n                    required: []\n                }\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(categoriesWithAttributes);\n    } catch (err) {\n        console.error(\"获取资产分类失败:\", err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"获取资产分类失败\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 创建新的资产分类\nasync function POST(request) {\n    try {\n        const categoryData = await request.json();\n        // 读取现有分类数据\n        const categoriesPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"..\", \"categories.json\");\n        const categoriesData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(categoriesPath, \"utf8\");\n        const categories = JSON.parse(categoriesData);\n        // 生成新的ID\n        const maxId = Math.max(...categories.map((cat)=>cat.id || 0));\n        const newCategory = {\n            ...categoryData,\n            id: maxId + 1,\n            created_at: new Date().toISOString(),\n            updated_at: null\n        };\n        // 添加新分类到数组\n        categories.push(newCategory);\n        // 写回文件\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(categoriesPath, JSON.stringify(categories, null, 2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newCategory);\n    } catch (err) {\n        console.error(\"创建资产分类失败:\", err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"创建资产分类失败\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/asset-categories/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fasset-categories%2Froute&page=%2Fapi%2Fasset-categories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fasset-categories%2Froute.ts&appDir=%2Fopt%2FRS_asset%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fopt%2FRS_asset%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();